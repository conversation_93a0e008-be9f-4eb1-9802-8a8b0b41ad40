2025-07-30 10:47:14,358 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:14,359 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:14,359 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 10:47:14,360 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 10:47:14,361 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 10:47:14,361 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 10:47:14,361 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 10:47:14,363 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 10:47:14,364 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 10:47:14,364 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 10:47:14,364 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 10:47:14,367 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:14,370 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_104714.log
2025-07-30 10:47:14,371 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 10:47:14,371 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 10:47:14,372 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 10:47:14,372 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 10:47:14,372 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 10:47:14,373 - __main__ - INFO - 📅 当前北京时间: 2025-07-30 18:47:14
2025-07-30 10:47:14,373 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 10:47:14,373 - __main__ - INFO - 📅 启动时间: 2025-07-30 18:47:14
2025-07-30 10:47:14,374 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 10:47:14,374 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 10:47:14,923 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:47:14,924 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 197994, 进程: Weixin.exe)
2025-07-30 10:47:15,449 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 10:47:15,450 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 525668, 进程: Weixin.exe)
2025-07-30 10:47:15,452 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 10:47:15,452 - __main__ - INFO - ✅ 找到 2 个微信窗口
2025-07-30 10:47:15,453 - __main__ - INFO -   窗口 1: 微信 (句柄: 197994)
2025-07-30 10:47:15,453 - __main__ - INFO -   窗口 2: 微信 (句柄: 525668)
2025-07-30 10:47:15,453 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 10:47:16,341 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 10:47:16,341 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 10:47:16,342 - __main__ - INFO - 📋 待处理联系人数: 2921
2025-07-30 10:47:16,342 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 10:47:16,342 - __main__ - INFO - 📊 总窗口数: 2, 总联系人数: 2921
2025-07-30 10:47:16,343 - __main__ - INFO - 
============================================================
2025-07-30 10:47:16,343 - __main__ - INFO - 🎯 开始处理第 1/2 个微信窗口 (第 1 轮)
2025-07-30 10:47:16,343 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:47:16,343 - __main__ - INFO - 📊 全局进度：已处理 0/2921 个联系人
2025-07-30 10:47:16,344 - __main__ - INFO - ============================================================
2025-07-30 10:47:16,344 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 10:47:16,344 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 197994)
2025-07-30 10:47:16,345 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 10:47:16,345 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 10:47:16,346 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:47:16,663 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:47:16,664 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:47:16,664 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:47:16,664 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:47:16,665 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:47:16,665 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:47:16,666 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:47:16,666 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:47:16,666 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:47:16,666 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:47:16,868 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:47:16,869 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:47:16,869 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 197994
2025-07-30 10:47:16,869 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 197994) - 增强版
2025-07-30 10:47:17,173 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 10:47:17,173 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 10:47:17,174 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:47:17,174 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 10:47:17,174 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 10:47:17,175 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:47:17,175 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:47:17,175 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:47:17,176 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:47:17,176 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 10:47:17,377 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 10:47:17,378 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 10:47:17,379 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 197994 (API返回: None)
2025-07-30 10:47:17,680 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 10:47:17,681 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 10:47:17,681 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 10:47:17,681 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 10:47:17,682 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 10:47:17,682 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 10:47:17,682 - __main__ - INFO - ✅ 步骤1：窗口管理完成
2025-07-30 10:47:17,682 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 10:47:18,683 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 10:47:18,684 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 10:47:18,684 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 10:47:18,684 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 10:47:18,684 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 10:47:18,685 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 10:47:18,685 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 10:47:18,685 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 10:47:18,886 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 10:47:18,887 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 10:47:21,265 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 10:47:21,265 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 10:47:21,266 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.4 秒...
